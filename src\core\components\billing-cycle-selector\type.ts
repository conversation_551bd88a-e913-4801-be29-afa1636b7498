import { FormControlProps } from '@mui/material';

export interface BillingCycleOption {
  value: 'monthly' | 'yearly';
  label: string;
  pricing?: number;
}

export interface BillingCycleSelectorProps {
  value: 'monthly' | 'yearly';
  onChange: (value: 'monthly' | 'yearly') => void;
  options?: BillingCycleOption[];
  className?: string;
  disabled?: boolean;
  label?: string;
  placeholder?: string;
  showPricing?: boolean;
  variant?: 'select' | 'toggle';
  error?: boolean;
  helperText?: string;
  required?: boolean;
  formControlProps?: FormControlProps;
}
