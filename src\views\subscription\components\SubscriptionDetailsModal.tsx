import React, { useCallback, useState } from 'react';

import { useRouter } from 'next/navigation';

import { useSubscriptionStore } from '@/store/subscription';
import { useUserStore } from '@/store/userStore';

import {
  createSubscriptionPaymentOrder,
  CreateSubscriptionPaymentOrderRequest,
} from '@/query/payments';
import {
  subscribeAfterPayment,
  changePlanSubscription,
  startTrial,
  getSubscriptionOrganizationId,
} from '@/query/subscription';

import {
  openRazorpayCheckout,
  getRazorpayKeyId,
  RazorpayResponse,
} from '@/utils/razorpay';
import { renderHTMLContent } from '@/utils/renderHTMLContent';
import {
  calculateTotalWithTax,
  getProratedBillingInfo,
} from '@/utils/subscription';
import { clearSubscriptionUserData } from '@/utils/subscription';

import GreenCheckIcon from '@/assets/icons/GreenCheckIcon';

import AppModal from '@/core/components/app-modal';
import AppTextField from '@/core/components/app-text-field';

import { Plan } from '../PlanCard';

import PaymentFailureModal from './PaymentFailureModal';
import PaymentSuccessModal from './PaymentSuccessModal';
import ProratedBillingBreakdownModal from './ProratedBillingBreakdownModal';

interface SubscriptionDetailsModalProps {
  open: boolean;
  isFromProfile?: boolean;
  onClose: () => void;
  plan: Plan;
  billingCycle: 'monthly' | 'yearly';
  nextPlan?: Plan | null;
  currentPlanId?: string;
  selectedPlanId?: string;
  allPlans?: Plan[];
  subscriberData?: any; // Add subscriber data for prorated billing
  pricing: {
    premiumAmount: number;
    subTotal: number;
    tax: number;
    totalAmount: number;
    featuresTotal?: number;
  };
  billingDetails: {
    name: string;
    email: string;
    promoCode: string;
  };
  onBillingDetailsChange: (field: string, value: string) => void;
  onProceed: () => void;
  onUpgradeClick: () => void;
  onAddFeaturesClick: (plan?: any) => void;
  isLoading: boolean;
}

// Helper function to check if add-on features are actually available
const hasAddOnFeatures = (plan: Plan): boolean => {
  if (!plan.apiPlan?.addOnFeatures) return false;

  const { MRD, EMR, Billing } = plan.apiPlan.addOnFeatures;
  return (
    (MRD?.length || 0) > 0 ||
    (EMR?.length || 0) > 0 ||
    (Billing?.length || 0) > 0
  );
};

const SubscriptionDetailsModal: React.FC<SubscriptionDetailsModalProps> = ({
  open,
  onClose,
  plan,
  billingCycle,
  nextPlan,
  allPlans,
  subscriberData,
  pricing,
  billingDetails,
  onBillingDetailsChange,
  onProceed,
  onUpgradeClick,
  onAddFeaturesClick,
  isLoading,
  isFromProfile,
  currentPlanId,
  selectedPlanId,
}) => {
  const {
    selectedAddOnFeatures,
    setSelectedAddOnFeatures,
    validateUserEmail,
    fetchSubscriber,
  } = useSubscriptionStore();
  const { data: userStoreData, fetchUser } = useUserStore();
  const router = useRouter();

  // Local loading state for subscription operations
  const [isProcessingSubscription, setIsProcessingSubscription] =
    useState(false);

  // Success and failure modal states
  const [showPaymentSuccessModal, setShowPaymentSuccessModal] = useState(false);
  const [showPaymentFailureModal, setShowPaymentFailureModal] = useState(false);
  const [trialErrorMessage, setTrialErrorMessage] = useState<
    string | React.ReactElement
  >('');

  // State to track if we're showing upgrade plan
  const [isShowingUpgradePlan, setIsShowingUpgradePlan] = useState(false);

  // State for prorated billing breakdown modal
  const [showProratedBreakdownModal, setShowProratedBreakdownModal] =
    useState(false);

  // State to track if subscription modal should be hidden when price breakup modal is open
  const [isSubscriptionModalHidden, setIsSubscriptionModalHidden] =
    useState(false);

  // State to track focused fields for clearing placeholders
  const [focusedFields, setFocusedFields] = useState({
    name: false,
    email: false,
    pincode: false,
  });

  // Handle modal close with cleanup
  const handleModalClose = useCallback(() => {
    if (!isProcessingSubscription) {
      // Clear selected features when closing modal
      setSelectedAddOnFeatures([]);

      // Reset local states
      setShowPaymentSuccessModal(false);
      setShowPaymentFailureModal(false);
      setTrialErrorMessage('');

      // Reset upgrade plan state
      setIsShowingUpgradePlan(false);

      // Reset focused fields
      setFocusedFields({ name: false, email: false, pincode: false });

      // Call the original onClose handler
      onClose();
    }
  }, [isProcessingSubscription, setSelectedAddOnFeatures, onClose]);

  // Handle field focus to clear placeholder
  const handleFieldFocus = (field: keyof typeof focusedFields) => {
    setFocusedFields((prev) => ({ ...prev, [field]: true }));
  };

  // Handle field blur to restore placeholder
  const handleFieldBlur = (field: keyof typeof focusedFields) => {
    setFocusedFields((prev) => ({ ...prev, [field]: false }));
  };

  // Effect to cleanup when modal closes automatically and on modal open
  React.useEffect(() => {
    if (!open && isProcessingSubscription) {
      // Clear selected features when modal closes automatically during processing
      setSelectedAddOnFeatures([]);
      setIsProcessingSubscription(false);
      setShowPaymentSuccessModal(false);
      setShowPaymentFailureModal(false);
      setTrialErrorMessage('');
      setIsShowingUpgradePlan(false);
    }
  }, [open, isProcessingSubscription, setSelectedAddOnFeatures]);

  // Clear add-on features when modal opens to start fresh
  React.useEffect(() => {
    if (open) {
      // Clear selected features when modal opens
      setSelectedAddOnFeatures([]);
    }
  }, [open, setSelectedAddOnFeatures]);

  // Calculate next plan based on plan amounts
  const calculateNextPlan = (): Plan | null => {
    if (!allPlans || allPlans.length === 0) return null;

    // Get current plan's amount based on billing cycle
    const currentPlanAmount = plan.apiPlan
      ? billingCycle === 'monthly'
        ? (plan.apiPlan.totalMonthlyBasicAmount ?? plan.monthlyPrice)
        : (plan.apiPlan.totalYearlyBasicAmount ?? plan.yearlyPrice)
      : billingCycle === 'monthly'
        ? plan.monthlyPrice
        : plan.yearlyPrice;

    // Filter plans that have higher amount and are not organization plans
    const higherPlans = allPlans
      .filter((p) => {
        if (p.isOrganizationPlan || p.id === plan.id) return false;

        const planAmount = p.apiPlan
          ? billingCycle === 'monthly'
            ? (p.apiPlan.totalMonthlyBasicAmount ?? p.monthlyPrice)
            : (p.apiPlan.totalYearlyBasicAmount ?? p.yearlyPrice)
          : billingCycle === 'monthly'
            ? p.monthlyPrice
            : p.yearlyPrice;

        return planAmount > currentPlanAmount;
      })
      .sort((a, b) => {
        const amountA = a.apiPlan
          ? billingCycle === 'monthly'
            ? (a.apiPlan.totalMonthlyBasicAmount ?? a.monthlyPrice)
            : (a.apiPlan.totalYearlyBasicAmount ?? a.yearlyPrice)
          : billingCycle === 'monthly'
            ? a.monthlyPrice
            : a.yearlyPrice;

        const amountB = b.apiPlan
          ? billingCycle === 'monthly'
            ? (b.apiPlan.totalMonthlyBasicAmount ?? b.monthlyPrice)
            : (b.apiPlan.totalYearlyBasicAmount ?? b.yearlyPrice)
          : billingCycle === 'monthly'
            ? b.monthlyPrice
            : b.yearlyPrice;

        return amountA - amountB;
      });

    // Return the first (cheapest) higher plan
    return higherPlans.length > 0 ? higherPlans[0] : null;
  };

  const calculatedNextPlan = calculateNextPlan();

  // Get the current plan to display (either original or upgrade plan)
  const currentDisplayPlan =
    isShowingUpgradePlan && calculatedNextPlan ? calculatedNextPlan : plan;

  // Check if billing cycle is different from current subscription (for prorated billing)
  const currentSubscriptionBillingType =
    subscriberData?.activeSubscription?.billingType;
  const isDifferentBillingCycle =
    isFromProfile &&
    currentSubscriptionBillingType &&
    currentSubscriptionBillingType !== billingCycle;

  // Check if selected plan is different from current plan (for profile flows)
  const isDifferentPlan =
    isFromProfile && currentPlanId && currentDisplayPlan.id !== currentPlanId;

  // PRORATED BILLING CALCULATION FOR UPGRADES OR BILLING CYCLE CHANGES
  const proratedInfo = getProratedBillingInfo(
    isFromProfile || false,
    isDifferentPlan || isDifferentBillingCycle || false,
    subscriberData,
    billingCycle,
    plan,
    isShowingUpgradePlan,
    calculatedNextPlan
  );

  // Get current pricing based on the displayed plan
  const getBasePricing = () => {
    if (isShowingUpgradePlan && calculatedNextPlan) {
      // For upgrade plans, show actual plan amount
      return {
        premiumAmount:
          billingCycle === 'monthly'
            ? (calculatedNextPlan.apiPlan?.totalMonthlyBasicAmount ??
              calculatedNextPlan.monthlyPrice)
            : (calculatedNextPlan.apiPlan?.totalYearlyBasicAmount ??
              calculatedNextPlan.yearlyPrice),
      };
    }

    // For current plan in profile view, handle billing cycle changes
    if (
      isFromProfile &&
      currentPlanId &&
      currentDisplayPlan.id === currentPlanId
    ) {
      // If billing cycle is different, calculate prorated amount
      if (isDifferentBillingCycle && proratedInfo) {
        return {
          premiumAmount: proratedInfo.proratedPremiumAmount, // Use prorated amount for billing cycle change
        };
      }

      // Otherwise, current plan is already paid
      return {
        premiumAmount: 0, // Current plan is already paid
      };
    }

    // For other cases, use original pricing
    return {
      premiumAmount: pricing.premiumAmount,
    };
  };

  const basePricing = getBasePricing();

  // Calculate features total only for features available in current plan
  const availableFeatures = currentDisplayPlan?.apiPlan
    ? (Object.values(currentDisplayPlan.apiPlan.addOnFeatures).flat() as any[])
    : [];
  const currentPlanFeatureIds = availableFeatures.map((f: any) => f.featureId);

  const featuresTotal = selectedAddOnFeatures
    .filter((feature) => currentPlanFeatureIds.includes(feature.featureId))
    .reduce((sum, feature) => {
      const cost =
        billingCycle === 'monthly'
          ? feature.monthlyAmount
          : feature.yearlyAmount;
      return sum + cost;
    }, 0);

  const hasFeaturesFromCurrentPlan = selectedAddOnFeatures.some((feature) =>
    currentPlanFeatureIds.includes(feature.featureId)
  );

  // Check if user has added any extra features
  const hasExtraFeatures = selectedAddOnFeatures.length > 0;

  // Check if user already has all available add-on features for the current plan
  const hasAllAvailableFeatures = () => {
    if (
      !subscriberData?.activeSubscription ||
      !currentPlanId ||
      currentDisplayPlan.id !== currentPlanId
    ) {
      return false;
    }

    const currentSubscribedFeatures =
      subscriberData.activeSubscription.addOnFeatures || {};
    const allSubscribedFeatureIds = [
      ...(currentSubscribedFeatures.MRD || []).map((f: any) => f.featureId),
      ...(currentSubscribedFeatures.EMR || []).map((f: any) => f.featureId),
      ...(currentSubscribedFeatures.Billing || []).map((f: any) => f.featureId),
    ];

    const allAvailableFeatureIds = availableFeatures.map(
      (f: any) => f.featureId
    );

    // Check if all available features are already subscribed
    return allAvailableFeatureIds.every((featureId) =>
      allSubscribedFeatureIds.includes(featureId)
    );
  };

  const userHasAllAvailableFeatures = hasAllAvailableFeatures();

  // Calculate next billing date
  const getNextBillingDate = () => {
    if (!subscriberData?.activeSubscription) return null;

    const endDate = new Date(subscriberData.activeSubscription.endDate);
    // Format date as "20 Aug 2026"
    return endDate.toLocaleDateString('en-GB', {
      day: 'numeric',
      month: 'short',
      year: 'numeric',
    });
  };

  const nextBillingDate = getNextBillingDate();

  // Updated pricing with features
  const updatedPricing = {
    ...basePricing,
    featuresTotal,
    subTotal: proratedInfo
      ? proratedInfo.proratedPremiumAmount + featuresTotal
      : basePricing.premiumAmount + featuresTotal,
  };

  // Calculate tax (18% of subtotal)
  const taxAmount = updatedPricing.subTotal * 0.18;
  const totalAmount = updatedPricing.subTotal + taxAmount;

  // Disable proceed button if same plan and same billing cycle and no extra features added
  const shouldDisableProceed =
    isFromProfile &&
    !isDifferentPlan &&
    !isDifferentBillingCycle &&
    !hasExtraFeatures;

  // Check if this is a subscription payment flow for non-free plans (cost not zero and not organization plan)
  const isPaidSubscriptionPayment =
    totalAmount > 0 && !currentDisplayPlan.isOrganizationPlan;

  // Handle paid subscription payment flow
  const handlePaidSubscriptionPayment = useCallback(async () => {
    setIsProcessingSubscription(true);

    try {
      // For profile flows, skip email verification and proceed directly
      if (!isFromProfile) {
        // Step 1: Validate email before proceeding with payment (only for non-profile flows)
        const emailValidationResult = await validateUserEmail(
          billingDetails.email
        );

        if (!emailValidationResult.canProceed) {
          // If cannot proceed, show failure modal and start timer
          setTrialErrorMessage(emailValidationResult.message);
          setShowPaymentFailureModal(true);
          setIsProcessingSubscription(false);

          // PaymentFailureModal will handle the 5-second countdown automatically
          return;
        }
      }

      // Helper function to get email from userStore or billingDetails (for profile flows)
      const getEmail = () => {
        if (isFromProfile && userStoreData?.email) {
          return userStoreData.email;
        }
        return billingDetails.email || '<EMAIL>';
      };

      // Helper function to get name from userStore or billingDetails (for profile flows)
      const getName = () => {
        if (isFromProfile && userStoreData?.name) {
          return userStoreData.name;
        }
        return billingDetails.name || 'User';
      };

      // Step 2: Get subscription organization ID dynamically
      const organizationId = await getSubscriptionOrganizationId();

      // Step 3: Create payment order data for the actual amount
      const paymentData: CreateSubscriptionPaymentOrderRequest = {
        amount: parseFloat(totalAmount.toFixed(2)), // Send exact amount with 2 decimal places
        currency: 'INR',
        paymentType: 'subscription',
        subscriberEmail: getEmail(),
        organizationId: organizationId,
        description: isFromProfile
          ? 'Clinic Plan Change'
          : 'Clinic Annual Subscription',
        metadata: {
          planId: currentDisplayPlan.id,
          subscriptionType: 'clinic',
          contactEmail: getEmail(),
          billingType: billingCycle,
        },
      };

      // Create payment order
      const order = await createSubscriptionPaymentOrder(paymentData);
      if (!order?.success) {
        // Show failure modal with API response
        setTrialErrorMessage('Failed to create payment order');
        setShowPaymentFailureModal(true);
        setIsProcessingSubscription(false);
        return;
      }

      // Open Razorpay
      const razorpayKeyId = getRazorpayKeyId() || order.data.keyId;
      const options = {
        key: razorpayKeyId,
        amount: order.data.amount,
        currency: order.data.currency,
        name: 'ARCA EHR',
        description: order.data.description,
        order_id: order.data.orderId,
        handler: async (response: RazorpayResponse) => {
          try {
            // Format add-on features by module
            const selectedAddOnFeaturesFormatted = {
              MRD: selectedAddOnFeatures
                .filter((f) => {
                  // Check if feature belongs to MRD module
                  const mrdFeatures =
                    currentDisplayPlan.apiPlan?.addOnFeatures?.MRD || [];
                  return mrdFeatures.some(
                    (feature: any) => feature.featureId === f.featureId
                  );
                })
                .map((f) => ({
                  featureId: f.featureId,
                  monthlyAmount: f.monthlyAmount,
                  yearlyAmount: f.yearlyAmount,
                })),
              EMR: selectedAddOnFeatures
                .filter((f) => {
                  // Check if feature belongs to EMR module
                  const emrFeatures =
                    currentDisplayPlan.apiPlan?.addOnFeatures?.EMR || [];
                  return emrFeatures.some(
                    (feature: any) => feature.featureId === f.featureId
                  );
                })
                .map((f) => ({
                  featureId: f.featureId,
                  monthlyAmount: f.monthlyAmount,
                  yearlyAmount: f.yearlyAmount,
                })),
              Billing: selectedAddOnFeatures
                .filter((f) => {
                  // Check if feature belongs to Billing module
                  const billingFeatures =
                    currentDisplayPlan.apiPlan?.addOnFeatures?.Billing || [];
                  return billingFeatures.some(
                    (feature: any) => feature.featureId === f.featureId
                  );
                })
                .map((f) => ({
                  featureId: f.featureId,
                  monthlyAmount: f.monthlyAmount,
                  yearlyAmount: f.yearlyAmount,
                })),
            };

            let result;

            if (isFromProfile) {
              // For profile flow, use change-plan API
              // Check if this is feature addition to same plan or plan upgrade
              const isSamePlan = currentPlanId === currentDisplayPlan.id;

              let allAddOnFeaturesFormatted;

              if (isSamePlan) {
                // Adding features to the same plan - merge existing + new features
                const currentSubscribedFeatures =
                  subscriberData?.activeSubscription?.addOnFeatures || {};

                allAddOnFeaturesFormatted = {
                  MRD: [
                    ...(currentSubscribedFeatures.MRD || []).map((f: any) => ({
                      featureId: f.featureId,
                      monthlyAmount: f.monthlyAmount,
                      yearlyAmount: f.yearlyAmount,
                    })),
                    ...selectedAddOnFeaturesFormatted.MRD,
                  ],
                  EMR: [
                    ...(currentSubscribedFeatures.EMR || []).map((f: any) => ({
                      featureId: f.featureId,
                      monthlyAmount: f.monthlyAmount,
                      yearlyAmount: f.yearlyAmount,
                    })),
                    ...selectedAddOnFeaturesFormatted.EMR,
                  ],
                  Billing: [
                    ...(currentSubscribedFeatures.Billing || []).map(
                      (f: any) => ({
                        featureId: f.featureId,
                        monthlyAmount: f.monthlyAmount,
                        yearlyAmount: f.yearlyAmount,
                      })
                    ),
                    ...selectedAddOnFeaturesFormatted.Billing,
                  ],
                };
              } else {
                // Upgrading to a different plan - only use newly selected features
                allAddOnFeaturesFormatted = selectedAddOnFeaturesFormatted;
              }

              const changePlanData = {
                email: getEmail(),
                newPlanId: currentDisplayPlan?.id,
                billingType: billingCycle,
                selectedAddOnFeatures: allAddOnFeaturesFormatted,
                paymentId: response.razorpay_payment_id,
              };

              result = await changePlanSubscription(changePlanData);
            } else {
              // For normal flow, use existing subscribeAfterPayment API
              // Get user data from localStorage to access role information
              const userData =
                typeof window !== 'undefined'
                  ? JSON.parse(
                      localStorage.getItem('subscription_user_data') || '{}'
                    )
                  : {};

              const subscriptionData = {
                email: billingDetails?.email ?? '',
                name: billingDetails?.name ?? '',
                phoneNumber: '', // Default phone number
                planId: currentDisplayPlan?.id,
                billingType: billingCycle,
                roleId: userData?.roleId ?? '', // Use stored role ID or fallback
                // userType: userData?.role ?? '',
                userRole: userData?.role ?? '',
                selectedAddOnFeatures: selectedAddOnFeaturesFormatted,
                paymentId: response.razorpay_payment_id,
                Billing: [
                  {
                    name: getName(),
                    email: getEmail(),
                    pincode: billingDetails.promoCode ?? '',
                  },
                ],
              };

              result = await subscribeAfterPayment(subscriptionData);
            }

            console.log('Subscribe result:', result);
            if (result.success) {
              // Show success modal immediately - data refresh will happen after modal closes
              setShowPaymentSuccessModal(true);
              // Keep isProcessingSubscription true to prevent modal from closing
              // It will be reset when success modal closes
            } else {
              // Display the actual error message from API response
              const errorMessage = result.message || 'Plan change failed';
              setTrialErrorMessage(errorMessage);
              setShowPaymentFailureModal(true);
              setIsProcessingSubscription(false);
            }
          } catch (error: any) {
            console.error('Error processing subscription:', error);
            // Show actual error message if available, handle HTTP errors properly
            const errorMessage =
              error?.response?.data?.message ||
              (error?.response?.data
                ? JSON.stringify(error.response.data)
                : null) ||
              error?.message ||
              `Request failed with status code ${error?.response?.status || 'Unknown'}`;
            setTrialErrorMessage(errorMessage);
            setShowPaymentFailureModal(true);
            setIsProcessingSubscription(false);
          }
        },
        prefill: {
          name: getName(),
          email: getEmail(),
          contact: '+919876543210',
        },
        theme: {
          color: '#3399cc',
        },
        modal: {
          ondismiss: () => {
            console.log('Payment modal dismissed');
            setTrialErrorMessage('Payment cancelled by user');
            setShowPaymentFailureModal(true);
            setIsProcessingSubscription(false);
          },
        },
      };

      await openRazorpayCheckout(options);
    } catch (error: any) {
      console.error('Error in subscription payment:', error);

      // Show actual error message from API if available, handle HTTP errors properly
      const errorMessage =
        error?.response?.data?.message ||
        (error?.response?.data ? JSON.stringify(error.response.data) : null) ||
        error?.message ||
        `Request failed with status code ${error?.response?.status || 'Unknown'}`;

      // Show failure modal for any API failures
      setTrialErrorMessage(errorMessage);
      setShowPaymentFailureModal(true);
      setIsProcessingSubscription(false);
    }
  }, [
    billingDetails,
    billingCycle,
    currentDisplayPlan,
    selectedAddOnFeatures,
    totalAmount,
    taxAmount,
    router,
    onClose,
    validateUserEmail,
    clearSubscriptionUserData,
    isFromProfile,
  ]);

  // Handle free trial subscription for non-profile flows
  const handleFreeTrialSubscription = useCallback(async () => {
    setIsProcessingSubscription(true);

    try {
      // Step 1: Validate email before proceeding with trial (only for non-profile flows)
      const emailValidationResult = await validateUserEmail(
        billingDetails.email
      );

      if (!emailValidationResult.canProceed) {
        // If cannot proceed, show failure modal and start timer
        setTrialErrorMessage(emailValidationResult.message);
        setShowPaymentFailureModal(true);
        setIsProcessingSubscription(false);

        // PaymentFailureModal will handle the 5-second countdown automatically
        return;
      }

      // Step 2: Get user data from localStorage to access role information
      const storedUserData =
        typeof window !== 'undefined'
          ? JSON.parse(localStorage.getItem('subscription_user_data') || '{}')
          : {};

      // Helper function to get email from userStore or billingDetails (for profile flows)
      const getEmail = () => {
        if (isFromProfile && userStoreData?.email) {
          return userStoreData.email;
        }
        return billingDetails.email || '';
      };

      // Helper function to get name from userStore or billingDetails (for profile flows)
      const getName = () => {
        if (isFromProfile && userStoreData?.name) {
          return userStoreData.name;
        }
        return billingDetails.name || '';
      };

      // Prepare API payload with role information
      const trialData = {
        email: getEmail(),
        name: getName(),
        phoneNumber: null,
        planId: currentDisplayPlan?.id,
        billingType: billingCycle,
        roleId: storedUserData?.roleId ?? '',
        userType: storedUserData?.role ?? 'admin',
        userRole: storedUserData?.role ?? 'admin',
        Billing: [
          {
            name: getName(),
            email: getEmail(),
            pincode: billingDetails.promoCode ?? '',
          },
        ],
      };

      console.log('Starting trial for free plan:', trialData);

      // Call startTrial API
      const result = await startTrial(trialData);

      if (result.success) {
        // Store trial data
        const { saveSubscriptionUserData } = await import(
          '@/utils/subscription'
        );
        saveSubscriptionUserData({
          selectedPlan: currentDisplayPlan?.id,
          subscriptionCompleted: true,
        });

        // Show success modal
        setShowPaymentSuccessModal(true);
        setIsProcessingSubscription(false);
        onClose();
      } else {
        // Check if the error message contains active subscription info
        const errorMessage =
          result.message || 'Failed to start trial. Please try again.';
        const isActiveSubscriptionError =
          errorMessage
            .toLowerCase()
            .includes('already have an active subscription') ||
          errorMessage.toLowerCase().includes('active subscription');

        if (isActiveSubscriptionError) {
          // Show error with additional info about active subscription
          setTrialErrorMessage(
            <div>
              <div className="mb-2">{errorMessage}</div>
              <div className="text-sm text-gray-600">
                You already have an active subscription. To change your plan,
                please upgrade or modify it from within the application.
              </div>
            </div>
          );
        } else {
          // Show actual error message from API in error modal (remove double quotes)
          setTrialErrorMessage(errorMessage.replace(/^"(.*)"$/, '$1'));
        }
        setShowPaymentFailureModal(true);
        setIsProcessingSubscription(false);
      }
    } catch (error: any) {
      console.error('Error starting trial:', error);

      // Show actual error message if available, handle HTTP errors properly
      const errorMessage =
        error?.response?.data?.message ||
        (error?.response?.data ? JSON.stringify(error.response.data) : null) ||
        error?.message ||
        `Request failed with status code ${error?.response?.status || 'Unknown'}`;

      // Remove double quotes from error message
      setTrialErrorMessage(errorMessage.replace(/^"(.*)"$/, '$1'));
      setShowPaymentFailureModal(true);
      setIsProcessingSubscription(false);
    }
  }, [
    billingDetails,
    currentDisplayPlan,
    billingCycle,
    onClose,
    validateUserEmail,
  ]);

  // Handle proceed button click
  const handleProceedClick = useCallback(() => {
    if (isPaidSubscriptionPayment) {
      handlePaidSubscriptionPayment();
    } else if (totalAmount === 0 && !currentDisplayPlan.isOrganizationPlan) {
      // For free plans
      if (isFromProfile) {
        // From profile, use change-plan API
        setIsProcessingSubscription(true);

        // Helper function to get email from userStore or billingDetails (for profile flows)
        const getEmail = () => {
          if (isFromProfile && userStoreData?.email) {
            return userStoreData.email;
          }
          return billingDetails.email || '';
        };

        // Check if this is feature addition to same plan or plan upgrade
        const isSamePlan = currentPlanId === currentDisplayPlan.id;

        let selectedAddOnFeaturesForFreePlan;

        if (isSamePlan) {
          // Adding features to the same plan - keep existing features
          const currentSubscribedFeatures =
            subscriberData?.activeSubscription?.addOnFeatures || {};
          selectedAddOnFeaturesForFreePlan = {
            MRD: currentSubscribedFeatures.MRD || [],
            EMR: currentSubscribedFeatures.EMR || [],
            Billing: currentSubscribedFeatures.Billing || [],
          };
        } else {
          // Upgrading to a different plan - use empty features for free plans
          selectedAddOnFeaturesForFreePlan = {
            MRD: [],
            EMR: [],
            Billing: [],
          };
        }

        const changePlanData = {
          email: getEmail(),
          newPlanId: currentDisplayPlan?.id,
          billingType: billingCycle,
          selectedAddOnFeatures: selectedAddOnFeaturesForFreePlan,
          paymentId: 'free_plan_no_payment', // Special payment ID for free plans
        };

        changePlanSubscription(changePlanData)
          .then(async (result) => {
            if (result.success) {
              // Show success modal immediately - data refresh will happen after modal closes
              setShowPaymentSuccessModal(true);
              // Keep isProcessingSubscription true to prevent modal from closing
              // It will be reset when success modal closes
            } else {
              const errorMessage = result.message || 'Failed to start trial';
              setTrialErrorMessage(errorMessage);
              setShowPaymentFailureModal(true);
              setIsProcessingSubscription(false);
            }
          })
          .catch((error: any) => {
            console.error('Error starting trial:', error);
            const errorMessage = error?.message || 'Failed to start trial';
            setTrialErrorMessage(errorMessage);
            setShowPaymentFailureModal(true);
            setIsProcessingSubscription(false);
          });
      } else {
        // Not from profile, add email verification then call startTrial
        handleFreeTrialSubscription();
      }
    } else {
      // For other cases, call the original proceed handler
      onProceed();
    }
  }, [
    isPaidSubscriptionPayment,
    handlePaidSubscriptionPayment,
    totalAmount,
    currentDisplayPlan,
    currentPlanId,
    isFromProfile,
    billingDetails,
    handleFreeTrialSubscription,
    onProceed,
  ]);

  // Handle upgrade button click - replace modal content with next plan
  const handleUpgradeClick = useCallback(() => {
    if (calculatedNextPlan) {
      // Replace modal content with next plan details
      setIsShowingUpgradePlan(true);
    } else {
      // For paid plan upgrades, call the onUpgradeClick callback
      onUpgradeClick();
    }
  }, [calculatedNextPlan, onUpgradeClick]);

  return (
    <>
      {!isSubscriptionModalHidden && (
        <AppModal
          open={open}
          onClose={handleModalClose}
          disableBackdropClick={true}
          title={
            <div className="flex items-center gap-3">
              {isShowingUpgradePlan && (
                <button
                  onClick={() => setIsShowingUpgradePlan(false)}
                  className="text-gray-600 hover:text-gray-800 p-1"
                >
                  <svg
                    width="20"
                    height="20"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                  >
                    <path d="M15 18l-6-6 6-6" />
                  </svg>
                </button>
              )}
              <span>Subscription Details</span>
            </div>
          }
          classes={{
            root: 'w-[900px] max-h-[90vh] px-1',
            body: 'p-8',
          }}
        >
          <div className="flex flex-col h-[70vh]">
            {/* Scrollable Content Area */}
            <div className="flex-1 overflow-y-auto">
              <div className="flex gap-10 p-2">
                {/* Left Side - Plan Details */}
                <div className="flex-1 space-y-6">
                  {/* Plan Header */}
                  <div>
                    <h3 className="text-2xl font-bold text-[#012436]">
                      {currentDisplayPlan.id === 'free-trial'
                        ? 'Free Trial (No Card Required)'
                        : currentDisplayPlan.name ===
                            'Professional (Recommended)'
                          ? 'Upgrade to Premium'
                          : currentDisplayPlan.name}
                    </h3>
                    <p className="text-[14px] font-normal text-black mt-1">
                      Valid for{' '}
                      {billingCycle === 'monthly' ? '30 Days' : '1 year'}
                    </p>
                  </div>

                  {/* Upgrade button for different plans in profile flow only */}
                  {isFromProfile &&
                    !isDifferentPlan &&
                    calculatedNextPlan &&
                    !isShowingUpgradePlan && (
                      <div className="flex items-center gap-2">
                        <span className="text-[14px] font-bold text-[#162B60]">
                          {currentDisplayPlan.monthlyPrice === 0 ||
                          currentDisplayPlan.yearlyPrice === 0
                            ? 'Upgrade to unlock more features'
                            : `Upgrade to ${calculatedNextPlan.name} for more benefits`}
                        </span>
                        <button
                          onClick={handleUpgradeClick}
                          className="bg-[#53BDF5] text-black px-4 py-1 rounded-[88px] text-[14px] font-normal hover:bg-[#42a8e0]"
                        >
                          Upgrade
                        </button>
                      </div>
                    )}

                  {/* Add Features button only if add-on features are available */}
                  {currentDisplayPlan.id !== 'free-trial' &&
                    hasAddOnFeatures(currentDisplayPlan) && (
                      <div className="flex items-center gap-2">
                        <span className="text-[14px] font-bold text-[#162B60]">
                          {userHasAllAvailableFeatures
                            ? 'Your available add on features'
                            : 'Add more features to customise your plan'}
                        </span>
                        <button
                          onClick={() => onAddFeaturesClick(currentDisplayPlan)}
                          className="bg-[#53BDF5] text-black px-4 py-1 rounded-[88px] text-[14px] font-normal hover:bg-[#42a8e0]"
                        >
                          Add Features
                        </button>
                      </div>
                    )}

                  {/* Plan Benefits */}
                  <div>
                    <h4 className="text-[16px] font-bold text-[#012436] mb-3">
                      Plan Benefits
                    </h4>

                    {/* Render HTML description if available */}
                    {currentDisplayPlan.apiPlan?.description && (
                      <div className="mb-4">
                        <div className="prose prose-sm max-w-none">
                          {renderHTMLContent(
                            currentDisplayPlan.apiPlan.description
                          )}
                        </div>
                      </div>
                    )}

                    {/* Render features list if available */}
                    {currentDisplayPlan.apiPlan?.features ? (
                      <div className="space-y-4">
                        <h5 className="text-[14px] font-semibold text-[#012436] mb-2">
                          Features Included:
                        </h5>

                        {/* MRD Module */}
                        {currentDisplayPlan.apiPlan.features.MRD &&
                          currentDisplayPlan.apiPlan.features.MRD.length >
                            0 && (
                            <div>
                              <div className="flex items-center space-x-2 mb-2">
                                <GreenCheckIcon width={17} height={17} />
                                <span className="text-[14px] font-semibold text-[#012436]">
                                  MRD
                                </span>
                              </div>
                              <div className="ml-6 space-y-1">
                                {currentDisplayPlan.apiPlan.features.MRD.map(
                                  (feature: any, index: number) => (
                                    <div
                                      key={index}
                                      className="flex items-center space-x-2"
                                    >
                                      <GreenCheckIcon width={12} height={12} />
                                      <span className="text-[12px] font-normal text-black">
                                        {feature.featureName}
                                      </span>
                                    </div>
                                  )
                                )}
                              </div>
                            </div>
                          )}

                        {/* EMR Module */}
                        {currentDisplayPlan.apiPlan.features.EMR &&
                          currentDisplayPlan.apiPlan.features.EMR.length >
                            0 && (
                            <div>
                              <div className="flex items-center space-x-2 mb-2">
                                <GreenCheckIcon width={17} height={17} />
                                <span className="text-[14px] font-semibold text-[#012436]">
                                  EMR
                                </span>
                              </div>
                              <div className="ml-6 space-y-1">
                                {currentDisplayPlan.apiPlan.features.EMR.map(
                                  (feature: any, index: number) => (
                                    <div
                                      key={index}
                                      className="flex items-center space-x-2"
                                    >
                                      <GreenCheckIcon width={12} height={12} />
                                      <span className="text-[12px] font-normal text-black">
                                        {feature.featureName}
                                      </span>
                                    </div>
                                  )
                                )}
                              </div>
                            </div>
                          )}

                        {/* Billing Module */}
                        {currentDisplayPlan.apiPlan.features.Billing &&
                          currentDisplayPlan.apiPlan.features.Billing.length >
                            0 && (
                            <div>
                              <div className="flex items-center space-x-2 mb-2">
                                <GreenCheckIcon width={17} height={17} />
                                <span className="text-[14px] font-semibold text-[#012436]">
                                  Billing
                                </span>
                              </div>
                              <div className="ml-6 space-y-1">
                                {currentDisplayPlan.apiPlan.features.Billing.map(
                                  (feature: any, index: number) => (
                                    <div
                                      key={index}
                                      className="flex items-center space-x-2"
                                    >
                                      <GreenCheckIcon width={12} height={12} />
                                      <span className="text-[12px] font-normal text-black">
                                        {feature.featureName}
                                      </span>
                                    </div>
                                  )
                                )}
                              </div>
                            </div>
                          )}

                        {/* Add On Features Available */}
                        {hasAddOnFeatures(currentDisplayPlan) && (
                          <div className="flex items-center space-x-2 mt-3">
                            <GreenCheckIcon width={16} height={16} />
                            <span className="text-[14px] font-medium text-[#012436]">
                              Add on features available
                            </span>
                          </div>
                        )}
                      </div>
                    ) : currentDisplayPlan.features &&
                      currentDisplayPlan.features.length > 0 ? (
                      <div className="space-y-2">
                        <h5 className="text-[14px] font-semibold text-[#012436] mb-2">
                          Features Included:
                        </h5>
                        {currentDisplayPlan.features.map((feature, index) => (
                          <div
                            key={index}
                            className="flex items-center space-x-2"
                          >
                            <GreenCheckIcon width={16} height={16} />
                            <span className="text-[14px] font-normal text-black">
                              {feature}
                            </span>
                          </div>
                        ))}
                        {/* Add On Features Available for fallback format */}
                        {hasAddOnFeatures(currentDisplayPlan) && (
                          <div className="flex items-center space-x-2 mt-3">
                            <GreenCheckIcon width={16} height={16} />
                            <span className="text-[14px] font-medium text-[#012436]">
                              Add on features available
                            </span>
                          </div>
                        )}
                      </div>
                    ) : null}
                  </div>

                  {/* Billing Details */}
                  <div>
                    <h4 className="text-[16px] font-bold text-[#012436] mb-3">
                      Billing Details
                    </h4>
                    <div className="space-y-4 max-w-[300px]">
                      <AppTextField
                        value={billingDetails.name}
                        onChange={(value) =>
                          onBillingDetailsChange('name', value)
                        }
                        placeholder={focusedFields.name ? '' : 'Bruce Wayne'}
                        onFocus={() => handleFieldFocus('name')}
                        onBlur={() => handleFieldBlur('name')}
                        classes={{
                          root: 'h-8',
                        }}
                      />
                      <AppTextField
                        value={billingDetails.email}
                        onChange={(value) =>
                          onBillingDetailsChange('email', value)
                        }
                        placeholder={
                          focusedFields.email ? '' : '<EMAIL>'
                        }
                        onFocus={() => handleFieldFocus('email')}
                        onBlur={() => handleFieldBlur('email')}
                        classes={{
                          root: 'h-8',
                        }}
                      />
                      <AppTextField
                        value={billingDetails.promoCode}
                        onChange={(value) =>
                          onBillingDetailsChange('promoCode', value)
                        }
                        placeholder={
                          focusedFields.pincode ? '' : 'Enter Pincode*'
                        }
                        onFocus={() => handleFieldFocus('pincode')}
                        onBlur={() => handleFieldBlur('pincode')}
                        classes={{
                          root: 'h-8',
                        }}
                      />
                    </div>

                    {/* Next Billing Date */}
                    {nextBillingDate && (
                      <div className="mt-5">
                        <div className="text-md font-medium text-[#012436]">
                          Next Billing Date:{' '}
                          <span className="font-bold ms-5">
                            {nextBillingDate}
                          </span>
                        </div>
                      </div>
                    )}
                  </div>
                </div>

                {/* Right Side - Pricing Summary (Scrollable) */}
                <div className="w-80 space-y-2 pr-2">
                  {/* PRORATED BILLING BREAKDOWN - Show for upgrades */}
                  {proratedInfo && (
                    <div className="space-y-2">
                      <button
                        onClick={() => {
                          setShowProratedBreakdownModal(true);
                          setIsSubscriptionModalHidden(true);
                        }}
                        className="text-[14px] font-normal text-[#162B60] underline"
                      >
                        View price breakup
                      </button>
                      <div className="flex justify-between border-b pb-2">
                        <span className="text-[14px] font-normal text-black">
                          Additional Amount to be Paid
                        </span>
                        <span className="text-[14px] font-normal text-black">
                          ₹{proratedInfo.proratedPremiumAmount.toFixed(2)}
                        </span>
                      </div>
                    </div>
                  )}

                  {!proratedInfo && (
                    <div className="flex justify-between  pb-2">
                      <span className="text-[14px] font-normal text-black">
                        {isFromProfile &&
                        currentPlanId &&
                        currentDisplayPlan.id === currentPlanId
                          ? 'Already Paid'
                          : 'Base Plan Amount'}
                      </span>
                      <span className="text-[14px] font-normal text-black">
                        {isFromProfile &&
                        currentPlanId &&
                        currentDisplayPlan.id === currentPlanId
                          ? (() => {
                              // Use the same calculation as in the current user section (payments-plans page)
                              const displayAmount =
                                subscriberData?.activeSubscription
                                  ?.totalAmount || 0;
                              const totalWithTax =
                                calculateTotalWithTax(displayAmount);
                              return `₹ ${totalWithTax}`;
                            })()
                          : `₹ ${basePricing.premiumAmount.toFixed(2)}`}
                      </span>
                    </div>
                  )}

                  {/* Features Added Section - Only show if add-on features exist AND items are from current plan */}
                  {hasAddOnFeatures(currentDisplayPlan) &&
                    hasFeaturesFromCurrentPlan && (
                      <>
                        <div className="flex justify-between">
                          <span className="text-[14px] font-normal text-black">
                            Feature added
                          </span>
                          <span className="text-[14px] font-normal text-black">
                            ₹ {featuresTotal.toFixed(2)}
                          </span>
                        </div>

                        {/* Individual features list - only show features from current plan */}
                        <div className="space-y-1">
                          {/* Remove button with underline */}
                          <button
                            onClick={() =>
                              onAddFeaturesClick(currentDisplayPlan)
                            }
                            className="text-[14px] font-normal underline transition-colors"
                          >
                            Remove{' '}
                          </button>
                        </div>
                      </>
                    )}

                  {/* Always show Sub Total */}
                  <div className="flex justify-between  border-b py-2">
                    <span className="text-[14px] font-medium text-black">
                      Sub Total
                    </span>
                    <span className="text-[14px] font-medium text-black">
                      ₹ {updatedPricing.subTotal.toFixed(2)}
                    </span>
                  </div>

                  {/* Tax set to 0 as requested */}
                  <div className="flex justify-between">
                    <span className="text-[14px] font-normal text-black">
                      Tax
                    </span>
                    <span className="text-[14px] font-normal text-black">
                      ₹ {taxAmount.toFixed(2)}
                    </span>
                  </div>
                </div>
              </div>
            </div>

            {/* Fixed Total Amount and Proceed Button at bottom */}
            <div className=" mt-4 pt-4 bg-white">
              <div className="flex justify-end">
                <div className="w-80 space-y-3 pr-2">
                  <div className="flex justify-between border-t border-b py-3">
                    <span className="text-[16px] font-bold text-black">
                      Total Amount :
                    </span>
                    <span className="text-[16px] font-bold text-black">
                      ₹ {totalAmount.toFixed(2)}
                    </span>
                  </div>

                  {/* Proceed Button */}
                  <button
                    onClick={handleProceedClick}
                    disabled={
                      isLoading ||
                      isProcessingSubscription ||
                      shouldDisableProceed
                    }
                    className="w-full py-2 px-3 bg-[#012436] text-white rounded-lg text-[14px] font-medium hover:bg-[#013547] transition-colors disabled:opacity-50"
                  >
                    {isLoading || isProcessingSubscription
                      ? 'Processing...'
                      : shouldDisableProceed
                        ? isFromProfile &&
                          currentPlanId &&
                          currentDisplayPlan.id === currentPlanId
                          ? 'Upgrade Plan or Add Features'
                          : 'Select Different Plan or Add Features'
                        : totalAmount === 0 &&
                            !currentDisplayPlan.isOrganizationPlan
                          ? 'Proceed'
                          : 'Proceed to Pay'}
                  </button>
                </div>
              </div>
            </div>
          </div>
        </AppModal>
      )}

      {/* Payment Success Modal */}
      <PaymentSuccessModal
        open={showPaymentSuccessModal}
        onClose={async () => {
          setShowPaymentSuccessModal(false);
          setIsProcessingSubscription(false);
          // Close the subscription details modal after success modal closes
          onClose();

          // Refresh user data after modal closes (for profile flows)
          if (isFromProfile) {
            try {
              setTimeout(() => {
                window.location.reload();
              }, 300);
            } catch (error) {
              console.error('Error refreshing user data:', error);
            }
          }
        }}
        isTrial={totalAmount === 0 && !currentDisplayPlan.isOrganizationPlan}
        billingCycle={billingCycle}
        isFromProfile={isFromProfile}
      />

      {/* Payment Failure Modal */}
      <PaymentFailureModal
        open={showPaymentFailureModal}
        onClose={() => {
          setShowPaymentFailureModal(false);
          setTrialErrorMessage('');
        }}
        errorMessage={trialErrorMessage}
        isFromProfile={isFromProfile}
      />

      {/* Prorated Billing Breakdown Modal */}
      {proratedInfo && (
        <ProratedBillingBreakdownModal
          open={showProratedBreakdownModal}
          onClose={() => {
            setShowProratedBreakdownModal(false);
            setIsSubscriptionModalHidden(false);
          }}
          proratedInfo={proratedInfo}
        />
      )}
    </>
  );
};

export default SubscriptionDetailsModal;
