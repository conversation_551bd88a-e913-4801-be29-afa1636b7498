'use client';

import React from 'react';

import AppSelect from '../app-select';

import { BillingCycleSelectorProps } from './type';

const BillingCycleSelector: React.FC<BillingCycleSelectorProps> = ({
  value,
  onChange,
  options,
  className = '',
  disabled = false,
  label = 'Billing Cycle',
  placeholder = 'Select billing cycle',
  showPricing = false,
  variant = 'select',
  error = false,
  helperText,
  required = false,
  ...rest
}) => {
  // Default options if none provided
  const defaultOptions = [
    {
      value: 'monthly',
      label: 'Monthly',
    },
    {
      value: 'yearly',
      label: 'Yearly',
    },
  ];

  const selectOptions = options || defaultOptions;

  const handleChange = (selectedOption: any) => {
    if (onChange) {
      onChange(selectedOption?.value || '');
    }
  };

  // Toggle variant for simple monthly/yearly toggle
  if (variant === 'toggle') {
    return (
      <div className={`flex items-center justify-center ${className}`}>
        <div className="bg-white rounded-full p-1 shadow-sm border">
          <button
            type="button"
            onClick={() => onChange('monthly')}
            disabled={disabled}
            className={`px-6 py-1.5 rounded-full text-sm font-medium transition-all duration-200 ${
              value === 'monthly'
                ? 'bg-gray-900 text-white'
                : 'text-gray-600 hover:text-gray-900'
            } ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}
          >
            Monthly
          </button>
          <button
            type="button"
            onClick={() => onChange('yearly')}
            disabled={disabled}
            className={`px-6 py-1.5 rounded-full text-sm font-medium transition-all duration-200 ${
              value === 'yearly'
                ? 'bg-gray-900 text-white'
                : 'text-gray-600 hover:text-gray-900'
            } ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}
          >
            Yearly
          </button>
        </div>
      </div>
    );
  }

  // Select variant using AppSelect
  return (
    <AppSelect
      label={label}
      placeholder={placeholder}
      options={selectOptions}
      value={selectOptions.find((option) => option.value === value) || null}
      onChange={handleChange}
      className={className}
      isDisabled={disabled}
      error={error}
      helperText={helperText}
      required={required}
      getOptionValue={(option) => option.value}
      getOptionLabel={(option) => {
        if (showPricing && option.pricing) {
          return `${option.label} - ₹${option.pricing}`;
        }
        return option.label;
      }}
      {...rest}
    />
  );
};

export default BillingCycleSelector;
